#!/bin/bash
### General options for full experiment (12 hours)
#BSUB -q gpua100                   # Queue name (choose based on GPU type)
#BSUB -J dual_cluster_full         # Job name
#BSUB -n 16                        # Number of CPU cores
#BSUB -gpu "num=1:mode=exclusive_process"  # One GPU in exclusive mode
#BSUB -R "rusage[mem=32GB]"        # 32 GB system memory
#BSUB -W 12:00                     # Walltime: 12 hours
#BSUB -o gpu_full_%J.out           # Output file
#BSUB -e gpu_full_%J.err           # Error file

# Load needed modules
module load cuda/11.6
module load python3/3.9.6

# Activate virtual environment if you have one
# source ~/venv/bin/activate

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export CUDA_VISIBLE_DEVICES=0
export OMP_NUM_THREADS=16

# Change to project directory
cd /zhome/bb/9/101964/xiuli/dual_clustering

echo "=== Starting Full Comprehensive Experiment ==="
echo "Job ID: $LSB_JOBID"
echo "Host: $(hostname)"
echo "Date: $(date)"
echo "Working directory: $(pwd)"

# Install dependencies
echo "=== Installing dependencies ==="
pip install --user -r requirements.txt

# Run full experiment suite
echo "=== Running full experiment suite ==="
python run_complete_experiments.py --step full --skip-deps

echo "=== Experiment completed ==="
echo "Results saved in: results/"
echo "Check results/comprehensive/ for detailed results"
echo "Check results/figures/ for visualizations"
