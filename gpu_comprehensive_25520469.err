Modules Release 5.3.0 (2023-05-14)
DCC-SW: Added modules (2023-aug/XeonGold6326)
DCC-SW: Removed standard modules: /apps/dcc/etc/ModulesAlma92/modulefiles
DCC-SW: Using gnu/12.3.0 compiler (requested)
Modules Release 4.8.0 (2021-07-14)
DCC-SW: Added modules (2023-aug/XeonGold6326)
DCC-SW: Removed standard modules: /apps/dcc/etc/ModulesAlma92/modulefiles
DCC-SW: Using gnu/12.3.0 compiler (requested)
Loaded module: cuda/12.6
Failed to run nbeats: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 1, the array at index 0 has size 1 and the array at index 1 has size 24
Failed to run proposed model: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Failed to run nbeats: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 1, the array at index 0 has size 1 and the array at index 1 has size 24
Failed to run proposed model: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Failed to run nbeats: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 1, the array at index 0 has size 1 and the array at index 1 has size 24
Failed to run proposed model: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Failed to run nbeats: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 1, the array at index 0 has size 1 and the array at index 1 has size 24
Failed to run proposed model: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Failed to run nbeats: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 1, the array at index 0 has size 1 and the array at index 1 has size 24
Failed to run proposed model: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Failed to run nbeats: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 1, the array at index 0 has size 1 and the array at index 1 has size 24
Failed to run proposed model: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Ablation full_model failed: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Ablation without_neutrosophic failed: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Ablation kmeans_only failed: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Ablation fcm_only failed: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Ablation without_indeterminacy failed: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Ablation distance_indeterminacy failed: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Sensitivity analysis for n_clusters=3 failed: boolean index did not match indexed array along axis 1; size of axis is 3 but size of corresponding boolean axis is 1
Sensitivity analysis for n_clusters=4 failed: boolean index did not match indexed array along axis 1; size of axis is 4 but size of corresponding boolean axis is 1
Sensitivity analysis for n_clusters=5 failed: boolean index did not match indexed array along axis 1; size of axis is 5 but size of corresponding boolean axis is 1
Sensitivity analysis for n_clusters=6 failed: boolean index did not match indexed array along axis 1; size of axis is 6 but size of corresponding boolean axis is 1
Sensitivity analysis for n_clusters=7 failed: boolean index did not match indexed array along axis 1; size of axis is 7 but size of corresponding boolean axis is 1
Sensitivity analysis for n_clusters=8 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for fcm_fuzziness=1.5 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for fcm_fuzziness=2.0 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for fcm_fuzziness=2.5 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for fcm_fuzziness=3.0 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for n_estimators=50 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for n_estimators=100 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for n_estimators=150 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for n_estimators=200 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for gamma=1.0 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for gamma=1.5 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for gamma=1.96 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for gamma=2.0 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for gamma=2.5 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for beta=0.5 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for beta=1.0 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for beta=1.5 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Sensitivity analysis for beta=2.0 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Computational analysis for NDC-RF with size 1000 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Computational analysis for NDC-RF with size 5000 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Computational analysis for NDC-RF with size 10000 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Computational analysis for NDC-RF with size 20000 failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Cross-dataset evaluation gefcom2014_energy -> kaggle_solar_plant failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
Cross-dataset evaluation kaggle_solar_plant -> gefcom2014_energy failed: boolean index did not match indexed array along axis 1; size of axis is 8 but size of corresponding boolean axis is 1
