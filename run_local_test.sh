#!/bin/bash
# Local test script to verify everything works before submitting to cluster

echo "=== Setting up environment ==="
# Install dependencies
pip install -r requirements.txt

echo "=== Running quick test ==="
# Run a quick test to verify everything works
python run_complete_experiments.py --step test --skip-deps --skip-data

echo "=== Running quick experiment ==="
# Run a quick experiment with minimal data
python run_complete_experiments.py --step quick --skip-deps

echo "=== Test completed ==="
echo "If this runs successfully, you can submit to the GPU cluster"
