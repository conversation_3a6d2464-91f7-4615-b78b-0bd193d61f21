
------------------------------------------------------------
Sender: LSF System <<EMAIL>>
Subject: Job 25518191: <dual_cluster_comprehensive> in cluster <dcc> Exited

Job <dual_cluster_comprehensive> was submitted from host <hpclogin2> by user <xiuli> in cluster <dcc> at Tue Jul  8 16:03:22 2025
Job was executed on host(s) <8*n-62-18-10>, in queue <gpua100>, as user <xiuli> in cluster <dcc> at Tue Jul  8 16:03:24 2025
</zhome/bb/9/101964> was used as the home directory.
</zhome/bb/9/101964/xiuli/dual_clustering> was used as the working directory.
Started at Tue Jul  8 16:03:24 2025
Terminated at Tue Jul  8 16:23:10 2025
Results reported at Tue Jul  8 16:23:10 2025

Your job looked like:

------------------------------------------------------------
# LSBATCH: User input
#!/bin/bash
### Comprehensive evaluation script for dual clustering experiments
#BSUB -q gpua100                   # Queue name (choose based on GPU type)
#BSUB -J dual_cluster_comprehensive # Job name
#BSUB -n 8                         # Number of CPU cores
#BSUB -gpu "num=1:mode=exclusive_process"  # One GPU in exclusive mode
#BSUB -R "rusage[mem=16GB]"        # 16 GB system memory
#BSUB -W 8:00                      # Walltime: 8 hours
#BSUB -o gpu_comprehensive_%J.out  # Output file
#BSUB -e gpu_comprehensive_%J.err  # Error file

# Source bashrc to set up local Python environment
source ~/.bashrc

# Load only CUDA module (using local Python)
module load cuda/12.6

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export CUDA_VISIBLE_DEVICES=0
export OMP_NUM_THREADS=8

# Change to project directory
cd /zhome/bb/9/101964/xiuli/dual_clustering

echo "=== Starting Comprehensive Evaluation ==="
echo "Job ID: $LSB_JOBID"
echo "Host: $(hostname)"
echo "Date: $(date)"
echo "Working directory: $(pwd)"

# Install dependencies
echo "=== Installing dependencies ==="
pip install --user -r requirements.txt

# Run comprehensive evaluation
echo "=== Running comprehensive evaluation ==="

# Run full comprehensive evaluation with all components
python experiments/comprehensive_evaluation.py --config benchmark_config

# Alternative: Run specific components only (uncomment if needed)
# python experiments/comprehensive_evaluation.py \
#     --config benchmark_config \
#     --datasets entso_e_solar entso_e_wind gefcom2014_solar \
#     --skip-computational --skip-cross-dataset --skip-robustness

echo "=== Comprehensive evaluation completed ==="
echo "Results saved in: results/comprehensive/"

------------------------------------------------------------

TERM_OWNER: job killed by owner.
Exited with signal termination: 14.

Resource usage summary:

    CPU time :                                   2325.23 sec.
    Max Memory :                                 15680 MB
    Average Memory :                             9637.50 MB
    Total Requested Memory :                     131072.00 MB
    Delta Memory :                               115392.00 MB
    Max Swap :                                   -
    Max Processes :                              6
    Max Threads :                                33
    Run time :                                   1202 sec.
    Turnaround time :                            1188 sec.

The output (if any) is above this job summary.



PS:

Read file <gpu_comprehensive_25518191.err> for stderr output of this job.

