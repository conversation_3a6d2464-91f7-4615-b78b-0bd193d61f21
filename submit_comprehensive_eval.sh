#!/bin/bash
### Direct comprehensive evaluation script
#BSUB -q gpua100                   # Queue name (choose based on GPU type)
#BSUB -J dual_cluster_comprehensive # Job name
#BSUB -n 8                         # Number of CPU cores
#BSUB -gpu "num=1:mode=exclusive_process"  # One GPU in exclusive mode
#BSUB -R "rusage[mem=16GB]"        # 16 GB system memory
#BSUB -W 6:00                      # Walltime: 6 hours
#BSUB -o gpu_comprehensive_%J.out  # Output file
#BSUB -e gpu_comprehensive_%J.err  # Error file

# Load needed modules
module load cuda/11.6
module load python3/3.9.6

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export CUDA_VISIBLE_DEVICES=0
export OMP_NUM_THREADS=8

# Change to project directory
cd /zhome/bb/9/101964/xiuli/dual_clustering

echo "=== Starting Comprehensive Evaluation ==="
echo "Job ID: $LSB_JOBID"
echo "Host: $(hostname)"
echo "Date: $(date)"
echo "Working directory: $(pwd)"

# Install dependencies
echo "=== Installing dependencies ==="
pip install --user -r requirements.txt

# Run comprehensive evaluation with specific options
echo "=== Running comprehensive evaluation ==="

# Option 1: Run main comparison experiments only
python experiments/comprehensive_evaluation.py \
    --config benchmark_config \
    --datasets entso_e_solar entso_e_wind gefcom2014_solar \
    --skip-sensitivity --skip-computational --skip-cross-dataset --skip-robustness

# Option 2: Run with ablation studies
# python experiments/comprehensive_evaluation.py \
#     --config benchmark_config \
#     --datasets entso_e_solar entso_e_wind \
#     --skip-computational --skip-cross-dataset --skip-robustness

# Option 3: Run everything (uncomment if you want full evaluation)
# python experiments/comprehensive_evaluation.py --config benchmark_config

echo "=== Comprehensive evaluation completed ==="
echo "Results saved in: results/comprehensive/"
