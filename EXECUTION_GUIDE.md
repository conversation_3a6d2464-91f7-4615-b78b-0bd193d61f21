# Execution Guide for Dual Clustering Experiments

## Overview

This guide provides step-by-step instructions for running the neutrosophic dual clustering experiments on your GPU cluster.

## Prerequisites

1. **Environment**: GPU cluster with CUDA support
2. **Python**: Python 3.8+ with pip
3. **Resources**: At least 8GB RAM, 1 GPU for main experiments

## Step 1: Local Testing (Recommended)

Before submitting to the cluster, test locally:

```bash
# Make the test script executable
chmod +x run_local_test.sh

# Run local test
./run_local_test.sh
```

## Step 2: Choose Your Experiment Type

### Quick Experiment (30 minutes)
- Tests basic functionality
- Runs on subset of data
- Good for debugging

```bash
chmod +x submit_quick_experiment.sh
bsub < submit_quick_experiment.sh
```

### Main Experiment (4 hours)
- Runs main comparison experiments
- Includes ablation studies
- Recommended for paper results

```bash
chmod +x submit_main_experiment.sh
bsub < submit_main_experiment.sh
```

### Full Experiment (12 hours)
- Complete experimental suite
- All baselines, all datasets
- Comprehensive analysis

```bash
chmod +x submit_full_experiment.sh
bsub < submit_full_experiment.sh
```

### Direct Comprehensive Evaluation (6 hours)
- Direct access to evaluation script
- More control over parameters

```bash
chmod +x submit_comprehensive_eval.sh
bsub < submit_comprehensive_eval.sh
```

## Step 3: Monitor Your Jobs

```bash
# Check job status
bjobs

# Check job details
bjobs -l <job_id>

# Check output (while running)
tail -f gpu_*_<job_id>.out

# Check errors
tail -f gpu_*_<job_id>.err
```

## Step 4: Collect Results

After completion, results will be in:

```
results/
├── comprehensive/
│   ├── comprehensive_evaluation_YYYYMMDD_HHMMSS.json
│   └── comprehensive_evaluation_YYYYMMDD_HHMMSS.log
├── figures/
│   ├── model_comparison.png
│   ├── statistical_significance.png
│   ├── ablation_study.png
│   └── ...
└── logs/
    └── experiment_run_YYYYMMDD_HHMMSS.log
```

## Available Experiment Components

### 1. Main Comparison
- Compares NDC-RF against 12 baseline models
- Statistical significance testing
- Multiple datasets

### 2. Ablation Studies
- Tests individual component contributions
- Without neutrosophic features
- Different clustering approaches

### 3. Sensitivity Analysis
- Hyperparameter robustness
- Number of clusters, fuzziness, etc.

### 4. Computational Analysis
- Training/prediction time
- Memory usage
- Scalability testing

### 5. Cross-Dataset Generalization
- Train on one dataset, test on another
- Domain adaptation capabilities

### 6. Robustness Analysis
- Performance under noise
- Missing data handling

## Customization Options

### Modify Experiment Configuration

Edit `config/experiment_configs/benchmark_config.yaml`:

```yaml
# Reduce runtime for testing
reproducibility:
  n_runs: 1  # Instead of 5

# Skip expensive components
computational_analysis:
  enabled: false

# Reduce dataset sizes
computational_analysis:
  dataset_sizes: [1000, 5000]  # Instead of [1000, 5000, 10000, 20000]
```

### Run Specific Components Only

```bash
# Only main comparison
python experiments/comprehensive_evaluation.py \
    --config benchmark_config \
    --skip-ablation --skip-sensitivity --skip-computational \
    --skip-cross-dataset --skip-robustness

# Only ablation study
python experiments/comprehensive_evaluation.py \
    --config benchmark_config \
    --skip-main --skip-sensitivity --skip-computational \
    --skip-cross-dataset --skip-robustness
```

## Troubleshooting

### Common Issues

1. **Memory errors**: Reduce dataset sizes or use fewer CPU cores
2. **CUDA errors**: Check GPU availability and CUDA version
3. **Import errors**: Ensure all dependencies are installed
4. **Timeout**: Increase walltime in job script

### Performance Optimization

1. **Use parallel processing**: Add `--parallel` flag
2. **Reduce runs**: Set `n_runs: 1` in config for testing
3. **Skip expensive components**: Use `--skip-*` flags

### Debug Mode

For debugging, run interactively:

```bash
# Request interactive session
bsub -Is -q gpua100 -n 4 -gpu "num=1" -R "rusage[mem=8GB]" -W 1:00 bash

# Then run experiments manually
python run_complete_experiments.py --step quick --skip-deps
```

## Expected Runtime

- **Quick**: 30 minutes
- **Main**: 4 hours  
- **Full**: 12 hours
- **Comprehensive**: 6 hours

## Next Steps

1. Start with quick experiment to verify setup
2. Run main experiment for paper results
3. Analyze results in `results/` directory
4. Generate additional visualizations if needed
5. Use results for your TNNLS paper submission
