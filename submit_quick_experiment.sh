#!/bin/bash
### General options for quick experiment (30 minutes)
#BSUB -q gpua100                   # Queue name (choose based on GPU type)
#BSUB -J dual_cluster_quick        # Job name
#BSUB -n 4                         # Number of CPU cores
#BSUB -gpu "num=1:mode=exclusive_process"  # One GPU in exclusive mode
#BSUB -R "rusage[mem=8GB]"         # 8 GB system memory
#BSUB -W 0:30                      # Walltime: 30 minutes
#BSUB -o gpu_quick_%J.out          # Output file
#BSUB -e gpu_quick_%J.err          # Error file

# Load needed modules
module load cuda/11.6
module load python3/3.9.6

# Activate virtual environment if you have one
# source ~/venv/bin/activate

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export CUDA_VISIBLE_DEVICES=0

# Change to project directory
cd /zhome/bb/9/101964/xiuli/dual_clustering

echo "=== Starting Quick Experiment ==="
echo "Job ID: $LSB_JOBID"
echo "Host: $(hostname)"
echo "Date: $(date)"
echo "Working directory: $(pwd)"

# Install dependencies if needed
echo "=== Installing dependencies ==="
pip install --user -r requirements.txt

# Run quick experiment
echo "=== Running quick experiment ==="
python run_complete_experiments.py --step quick --skip-deps

echo "=== Experiment completed ==="
echo "Results saved in: results/"
echo "Check results/comprehensive/ for detailed results"
echo "Check results/figures/ for visualizations"
